import { cn } from "@utils/cn";
import { useState } from "react";

export const AnimationExamples = () => {
  const [showFade, setShowFade] = useState(false);
  const [showSlide, setShowSlide] = useState(false);
  const [showScale, setShowScale] = useState(false);
  const [showHeight, setShowHeight] = useState(false);
  const [showCombined, setShowCombined] = useState(false);

  return (
    <div className="space-y-8 p-8">
      <h1 className="mb-8 font-bold text-3xl">Animation Examples for Show/Hide Content</h1>

      {/* 1. Fade Animation */}
      <div className="rounded-lg border p-6">
        <h2 className="mb-4 font-semibold text-xl">1. Fade Animation</h2>
        <button onClick={() => setShowFade(!showFade)} className="btn btn-primary mb-4">
          Toggle Fade
        </button>
        <div
          className={cn(
            "transition-opacity duration-500 ease-in-out",
            showFade ? "opacity-100" : "opacity-0",
          )}
        >
          <div className="rounded bg-blue-100 p-4">This content fades in and out smoothly</div>
        </div>
      </div>

      {/* 2. Slide Animation */}
      <div className="rounded-lg border p-6">
        <h2 className="mb-4 font-semibold text-xl">2. Slide Animation</h2>
        <button onClick={() => setShowSlide(!showSlide)} className="btn btn-primary mb-4">
          Toggle Slide
        </button>
        <div className="overflow-hidden">
          <div
            className={cn(
              "transition-transform duration-500 ease-in-out",
              showSlide ? "translate-x-0" : "-translate-x-full",
            )}
          >
            <div className="rounded bg-green-100 p-4">This content slides in from the left</div>
          </div>
        </div>
      </div>

      {/* 3. Scale Animation */}
      <div className="rounded-lg border p-6">
        <h2 className="mb-4 font-semibold text-xl">3. Scale Animation</h2>
        <button onClick={() => setShowScale(!showScale)} className="btn btn-primary mb-4">
          Toggle Scale
        </button>
        <div
          className={cn(
            "origin-center transition-transform duration-300 ease-in-out",
            showScale ? "scale-100" : "scale-0",
          )}
        >
          <div className="rounded bg-purple-100 p-4">
            This content scales in and out from center
          </div>
        </div>
      </div>

      {/* 4. Height Animation */}
      <div className="rounded-lg border p-6">
        <h2 className="mb-4 font-semibold text-xl">4. Height Animation (Accordion)</h2>
        <button onClick={() => setShowHeight(!showHeight)} className="btn btn-primary mb-4">
          Toggle Height
        </button>
        <div
          className={cn(
            "overflow-hidden transition-all duration-500 ease-in-out",
            showHeight ? "max-h-96 opacity-100" : "max-h-0 opacity-0",
          )}
        >
          <div className="rounded bg-yellow-100 p-4">
            <p>This content expands and collapses with height animation.</p>
            <p>Perfect for accordion-style components.</p>
            <p>The height smoothly transitions between 0 and auto.</p>
          </div>
        </div>
      </div>

      {/* 5. Combined Animation */}
      <div className="rounded-lg border p-6">
        <h2 className="mb-4 font-semibold text-xl">5. Combined Animation (Like Sidebar)</h2>
        <button onClick={() => setShowCombined(!showCombined)} className="btn btn-primary mb-4">
          Toggle Combined
        </button>
        <div
          className={cn(
            "transition-all duration-500 ease-in-out",
            showCombined
              ? "max-w-md translate-x-0 scale-100 opacity-100"
              : "max-w-0 translate-x-4 scale-95 overflow-hidden opacity-0",
          )}
        >
          <div className="whitespace-nowrap rounded bg-red-100 p-4">
            Combined: fade + slide + scale + width
          </div>
        </div>
      </div>

      {/* Animation Tips */}
      <div className="rounded-lg border bg-gray-50 p-6">
        <h2 className="mb-4 font-semibold text-xl">Animation Tips</h2>
        <ul className="space-y-2 text-sm">
          <li>
            <strong>Duration:</strong> 200-300ms for quick interactions, 500ms for dramatic effects
          </li>
          <li>
            <strong>Easing:</strong> ease-in-out for natural feel, ease-out for snappy interactions
          </li>
          <li>
            <strong>Performance:</strong> Use transform and opacity for best performance
          </li>
          <li>
            <strong>Accessibility:</strong> Respect prefers-reduced-motion for users who need it
          </li>
          <li>
            <strong>Staggering:</strong> Add delays for multiple elements animating together
          </li>
        </ul>
      </div>
    </div>
  );
};
