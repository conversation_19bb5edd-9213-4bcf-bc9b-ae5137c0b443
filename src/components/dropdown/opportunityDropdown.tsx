import { Select } from "@components/common/select";

export const OpportunityDropdown = () => {
  return (
    <div className="flex items-center gap-3">
      <label htmlFor="opportunity" className="text-h6">
        โอกาส
      </label>
      {/* <Select
        id="opportunity"
        options={_OPPORTUNITY_OPTIONS}
        variant="icon"
        size="sm"
        value={opportunity ?? "-"}
        onChange={setOpportunity}
      /> */}
    </div>
  );
};
