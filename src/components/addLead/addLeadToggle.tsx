import { faPlus } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useTranslation } from "react-i18next";
import { AddLeadForm } from "./addLeadForm";

export const AddLeadToggle = () => {
  const { t } = useTranslation();
  return (
    <div className="drawer drawer-end">
      <input id="my-drawer" type="checkbox" className="drawer-toggle" />
      <div className="drawer-content">
        <label
          htmlFor="my-drawer"
          className="btn btn-outline btn-dash border-info/50 text-base-300 shadow-none hover:border-secondary-content hover:bg-secondary-content hover:text-base-content"
        >
          <FontAwesomeIcon icon={faPlus} />
          {t("navigation.addLead")}
        </label>
      </div>
      <div className="drawer-side">
        <label
          htmlFor="my-drawer"
          aria-label="close sidebar"
          className="drawer-overlay"
        />
        <div className="h-full w-1/2 rounded-l-2xl bg-accent-content p-6 text-base-content">
          <div className="flex h-full flex-1 flex-col gap-6 rounded-lg border bg-base-100 p-6">
            <h3>{t("navigation.addLead")}</h3>
            <div className="min-h-0 flex-1">
              <AddLeadForm />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
